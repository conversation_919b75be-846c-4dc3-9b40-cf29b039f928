\chapter{Sprint 5: No\-ti\-fi\-ca\-tions Management and System Polish}

\section{Introduction}
This chapter details the design and implementation of Sprint 5, focusing on comprehensive no\-ti\-fi\-ca\-tions management and final system polish for the MED4SOLUTIONS platform. Sprint 5 implements a robust no\-ti\-fi\-ca\-tion system using Firebase Cloud Messaging (FCM) to ensure real-time com\-mu\-ni\-ca\-tion between patients, pharmacists, and delivery personnel. This sprint also includes final system optimizations, quality assurance testing, and deployment preparations to ensure the platform meets production-ready standards.

\section{Sprint 5 Requirements Analysis}

\subsection{No\-ti\-fi\-ca\-tions Management Requirements}
The no\-ti\-fi\-ca\-tion system encompasses comprehensive real-time com\-mu\-ni\-ca\-tion across all platform users:

\begin{itemize}
    \item \textbf{Prescription No\-ti\-fi\-ca\-tions}: Patients receive alerts for prescription renewals and pharmacy no\-ti\-fi\-ca\-tions when prescriptions are added.
    \item \textbf{Order Status No\-ti\-fi\-ca\-tions}: Real-time no\-ti\-fi\-ca\-tions for order status updates throughout the delivery process.
    \item \textbf{System No\-ti\-fi\-ca\-tions}: Administrative alerts for system maintenance, updates, and important announcements.
    \item \textbf{Cross-Platform Delivery}: Consistent no\-ti\-fi\-ca\-tion delivery across mobile (Flutter) and web (Angular) platforms.
    \item \textbf{No\-ti\-fi\-ca\-tion Management}: Users can read, mark as unread, and manage no\-ti\-fi\-ca\-tion preferences.
\end{itemize}

\subsection{System Polish Requirements}
The final sprint includes comprehensive system optimization and quality assurance:

\begin{itemize}
    \item \textbf{Performance Optimization}: Database query optimization, API response time improvements, and mobile app performance tuning.
    \item \textbf{User Experience Polish}: UI/UX refinements, accessibility improvements, and responsive design optimization.
    \item \textbf{Security Hardening}: Final security audits, vulnerability assessments, and compliance verification.
    \item \textbf{Documentation Completion}: API documentation, user manuals, and deployment guides.
    \item \textbf{Testing and QA}: Comprehensive testing across all platforms and user scenarios.
\end{itemize}

\section{Sprint 5 Use Case Analysis}

\subsection{No\-ti\-fi\-ca\-tions Management Use Case Diagram}
Figure \ref{fig:notifications_usecase} illustrates the no\-ti\-fi\-ca\-tions management use cases, showing how different user roles interact with the no\-ti\-fi\-ca\-tion system across mobile and web platforms.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint5/notifications_usecase.png}
\caption{No\-ti\-fi\-ca\-tions Management Use Case Diagram}
\label{fig:notifications_usecase}
\end{figure}

The no\-ti\-fi\-ca\-tion system supports multiple no\-ti\-fi\-ca\-tion types including prescription alerts, order status updates, and system announcements. Patients can manage no\-ti\-fi\-ca\-tion preferences, while pharmacists receive automated alerts for new prescriptions requiring package creation.

\subsection{Firebase Integration Use Case Diagram}
Figure \ref{fig:firebase_usecase} presents the Firebase Cloud Messaging integration use cases, demonstrating the technical implementation of real-time no\-ti\-fi\-ca\-tions across platforms.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint5/firebase_usecase.png}
\caption{Firebase Cloud Messaging Integration Use Case Diagram}
\label{fig:firebase_usecase}
\end{figure}

The Firebase integration enables real-time push no\-ti\-fi\-ca\-tions, token management, and cross-platform no\-ti\-fi\-ca\-tion delivery with support for both foreground and background message handling.

\section{Sprint 5 Backlog}
The Sprint 5 backlog focuses on implementing comprehensive no\-ti\-fi\-ca\-tions management and final system polish extracted from the main product backlog.

\begin{center}
\begin{longtable}{|c|>{\raggedright\arraybackslash}p{3.2cm}|c|>{\raggedright\arraybackslash}p{3.5cm}|c|>{\raggedright\arraybackslash}p{3.5cm}|c|}
\hline
\textbf{ID\_F} & \textbf{Feature} & \textbf{ID\_U} & \textbf{User Story} & \textbf{ID\_T} & \textbf{Task} & \textbf{State} \\
\hline
\endfirsthead

\hline
\textbf{ID\_F} & \textbf{Feature} & \textbf{ID\_U} & \textbf{User Story} & \textbf{ID\_T} & \textbf{Task} & \textbf{State} \\
\hline
\endhead

\hline
\endfoot

\hline
\caption{Sprint 5 Backlog with Tasks}
\label{table:sprint5_backlog}
\endlastfoot

6 & No\-ti\-fi\-ca\-tions Management & 6.1 & As a patient, I want to receive alerts for prescription renewals & 6.1.1 & Implement prescription renewal alert logic in backend & Done \\
\cline{5-7}
 &  &  &  & 6.1.2 & Create prescription renewal no\-ti\-fi\-ca\-tion UI in mobile app & Done \\
\cline{5-7}
 &  &  &  & 6.1.3 & Integrate Firebase messaging for prescription alerts & Done \\
\cline{3-7}
 &  & 6.2 & As a patient, I want to read and unread no\-ti\-fi\-ca\-tions & 6.2.1 & Implement no\-ti\-fi\-ca\-tion status management in backend & Done \\
\cline{5-7}
 &  &  &  & 6.2.2 & Create no\-ti\-fi\-ca\-tion list UI with read/unread states & Done \\
\cline{5-7}
 &  &  &  & 6.2.3 & Implement no\-ti\-fi\-ca\-tion marking functionality & Done \\
\cline{3-7}
 &  & 6.3 & As a pharmacist, I want to receive no\-ti\-fi\-ca\-tion when a patient add a prescription with a package type content & 6.3.1 & Implement prescription addition no\-ti\-fi\-ca\-tion trigger & Done \\
\cline{5-7}
 &  &  &  & 6.3.2 & Create pharmacist no\-ti\-fi\-ca\-tion interface in web app & Done \\
\cline{3-7}
 &  & 6.4 & As a system, I want to configure Firebase Cloud Messaging for real-time no\-ti\-fi\-ca\-tions & 6.4.1 & Setup Firebase project and configure FCM & Done \\
\cline{5-7}
 &  &  &  & 6.4.2 & Implement FCM service in backend with firebase-admin & Done \\
\cline{5-7}
 &  &  &  & 6.4.3 & Configure Firebase service worker for web no\-ti\-fi\-ca\-tions & Done \\
\cline{5-7}
 &  &  &  & 6.4.4 & Implement Firebase messaging service in Flutter & Done \\
\cline{5-7}
 &  &  &  & 6.4.5 & Handle FCM token registration and refresh & Done \\
\cline{5-7}
 &  &  &  & 6.4.6 & Implement foreground and background message handling & Done \\
\cline{3-7}
 &  & 6.5 & As a system, I want optimized performance and polished UI/UX across all platforms & 6.5.1 & Optimize database queries and API response times & Done \\
\cline{5-7}
 &  &  &  & 6.5.2 & Implement caching strategies for improved performance & Done \\
\cline{5-7}
 &  &  &  & 6.5.3 & Optimize mobile app bundle size and loading times & Done \\
\cline{5-7}
 &  &  &  & 6.5.4 & Refine mobile app UI components and animations & Done \\
\cline{5-7}
 &  &  &  & 6.5.5 & Polish web application responsive design & Done \\
\cline{5-7}
 &  &  &  & 6.5.6 & Implement accessibility improvements & Done \\
\cline{3-7}
 &  & 6.6 & As a system, I want comprehensive testing and quality assurance & 6.6.1 & Conduct end-to-end testing across all platforms & Done \\
\cline{5-7}
 &  &  &  & 6.6.2 & Perform security audit and vulnerability assessment & Done \\
\hline
\end{longtable}
\end{center}

\section{Firebase Cloud Messaging Implementation}

\subsection{Firebase Configuration Architecture}
The MED4SOLUTIONS platform implements Firebase Cloud Messaging (FCM) across all three tiers of the application architecture to ensure seamless real-time no\-ti\-fi\-ca\-tion delivery.

\subsubsection{Backend Firebase Configuration}
The NestJS backend integrates Firebase Admin SDK for server-side no\-ti\-fi\-ca\-tion management:

\begin{itemize}
    \item \textbf{Firebase Admin SDK}: Configured with service account credentials for secure server-to-client com\-mu\-ni\-ca\-tion
    \item \textbf{FCM Service}: Dedicated service class handling no\-ti\-fi\-ca\-tion payload creation and delivery
    \item \textbf{Token Management}: Secure storage and management of user FCM tokens in MongoDB
    \item \textbf{No\-ti\-fi\-ca\-tion Types}: Structured no\-ti\-fi\-ca\-tion system supporting prescription alerts, order updates, and system no\-ti\-fi\-ca\-tions
\end{itemize}

\subsubsection{Web Application Firebase Configuration}
The Angular web application implements Firebase SDK for browser-based no\-ti\-fi\-ca\-tions:

\begin{itemize}
    \item \textbf{Firebase SDK Integration}: Client-side Firebase configuration with environment-specific settings
    \item \textbf{Service Worker}: Custom Firebase messaging service worker for background no\-ti\-fi\-ca\-tion handling
    \item \textbf{Permission Management}: User permission requests and no\-ti\-fi\-ca\-tion preference handling
    \item \textbf{Foreground No\-ti\-fi\-ca\-tions}: Real-time no\-ti\-fi\-ca\-tion display for active web sessions
\end{itemize}

\subsubsection{Mobile Application Firebase Configuration}
The Flutter mobile application integrates Firebase messaging for native push no\-ti\-fi\-ca\-tions:

\begin{itemize}
    \item \textbf{Firebase Messaging Plugin}: Native Flutter plugin for iOS and Android no\-ti\-fi\-ca\-tion support
    \item \textbf{Token Registration}: Automatic FCM token generation and backend synchronization
    \item \textbf{Background Processing}: Background message handling for no\-ti\-fi\-ca\-tions received when app is closed
    \item \textbf{Local No\-ti\-fi\-ca\-tions}: Integration with flutter\_local\_notifications for enhanced no\-ti\-fi\-ca\-tion display
\end{itemize}

\section{Sprint 5 Design and Architecture}

\subsection{No\-ti\-fi\-ca\-tion System Class Diagram}
Figure \ref{fig:notification_class_diagram} presents the complete class structure for the no\-ti\-fi\-ca\-tion management system, showing the relationships between no\-ti\-fi\-ca\-tion entities, services, and Firebase integration components.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint5/notification_class_diagram.png}
\caption{No\-ti\-fi\-ca\-tion System Class Diagram}
\label{fig:notification_class_diagram}
\end{figure}

The class diagram illustrates the no\-ti\-fi\-ca\-tion domain model including No\-ti\-fi\-ca\-tion entities, FCM service classes, and repository patterns used for no\-ti\-fi\-ca\-tion persistence and delivery.

\subsection{Firebase Integration Class Diagram}
Figure \ref{fig:firebase_class_diagram} details the Firebase Cloud Messaging integration architecture across backend, web, and mobile platforms.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint5/firebase_class_diagram.png}
\caption{Firebase Integration Class Diagram}
\label{fig:firebase_class_diagram}
\end{figure}

This diagram shows the Firebase service classes, messaging handlers, and token management components that enable cross-platform no\-ti\-fi\-ca\-tion delivery.

\subsection{Prescription No\-ti\-fi\-ca\-tion Sequence}
Figure \ref{fig:prescription_notification_sequence} illustrates the complete flow when a patient adds a prescription and the pharmacist receives a no\-ti\-fi\-ca\-tion.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint5/prescription_notification_sequence.png}
\caption{Prescription Addition No\-ti\-fi\-ca\-tion Sequence Diagram}
\label{fig:sprint5_prescription_notification_sequence}
\end{figure}

This sequence demonstrates the integration between the mobile app, backend services, Firebase Cloud Messaging, and the web application when a prescription is added by a patient.

\subsection{Order Status No\-ti\-fi\-ca\-tion Sequence}
Figure \ref{fig:order_notification_sequence} presents the no\-ti\-fi\-ca\-tion flow for order status updates throughout the delivery process.

\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{img/sprint5/order_notification_sequence.png}
\caption{Order Status No\-ti\-fi\-ca\-tion Sequence Diagram}
\label{fig:order_notification_sequence}
\end{figure}

This sequence shows how order status changes trigger no\-ti\-fi\-ca\-tions to patients, ensuring real-time visibility into delivery progress.

\subsection{Firebase Token Management Sequence}
Figure \ref{fig:firebase_token_sequence} details the FCM token registration and management process across platforms.

\begin{figure}[htbp]
\centering
\includegraphics[width=0.75\textwidth,height=0.6\textheight,keepaspectratio]{report/img/sprint5/firebase_token_sequence_FinalVersion.png}
\caption{Firebase Token Management Sequence Diagram}
\label{fig:firebase_token_sequence}
\end{figure}

This sequence demonstrates how FCM tokens are generated, registered, and managed to ensure reliable no\-ti\-fi\-ca\-tion delivery.

\section{Technical Implementation Details}

\subsection{Backend No\-ti\-fi\-ca\-tion Service Architecture}
The backend implements a comprehensive no\-ti\-fi\-ca\-tion service using NestJS and Firebase Admin SDK:

\subsubsection{FCM Service Implementation}
\begin{itemize}
    \item \textbf{Service Initialization}: Firebase Admin SDK initialized with service account credentials
    \item \textbf{Message Construction}: Structured message payloads with no\-ti\-fi\-ca\-tion and data components
    \item \textbf{Platform-Specific Configuration}: Android and iOS specific no\-ti\-fi\-ca\-tion settings
    \item \textbf{Error Handling}: Comprehensive error handling for invalid tokens and delivery failures
    \item \textbf{Logging}: Detailed logging for no\-ti\-fi\-ca\-tion delivery tracking and debugging
\end{itemize}

\subsubsection{No\-ti\-fi\-ca\-tion Repository}
\begin{itemize}
    \item \textbf{MongoDB Integration}: No\-ti\-fi\-ca\-tion persistence with status tracking
    \item \textbf{User Token Management}: FCM token storage and updates
    \item \textbf{No\-ti\-fi\-ca\-tion History}: Complete no\-ti\-fi\-ca\-tion history for audit and analytics
    \item \textbf{Status Updates}: Read/unread status management and FCM delivery confirmation
\end{itemize}

\subsection{Web Application Firebase Integration}
The Angular web application implements comprehensive Firebase messaging support:

\subsubsection{Firebase Service}
\begin{itemize}
    \item \textbf{SDK Initialization}: Firebase app initialization with environment configuration
    \item \textbf{Permission Management}: User no\-ti\-fi\-ca\-tion permission requests and handling
    \item \textbf{Token Generation}: FCM token generation with VAPID key configuration
    \item \textbf{Message Handling}: Foreground message reception and browser no\-ti\-fi\-ca\-tion display
\end{itemize}

\subsubsection{Service Worker Configuration}
\begin{itemize}
    \item \textbf{Background Processing}: Service worker for background message handling
    \item \textbf{No\-ti\-fi\-ca\-tion Display}: Custom no\-ti\-fi\-ca\-tion display with action buttons
    \item \textbf{Click Handling}: No\-ti\-fi\-ca\-tion click handling with appropriate page navigation
    \item \textbf{Badge Management}: No\-ti\-fi\-ca\-tion badge updates and management
\end{itemize}

\subsection{Mobile Application Firebase Integration}
The Flutter mobile application provides native no\-ti\-fi\-ca\-tion support across iOS and Android:

\subsubsection{Firebase Messaging Service}
\begin{itemize}
    \item \textbf{Plugin Integration}: Firebase messaging plugin configuration
    \item \textbf{Permission Requests}: Platform-specific no\-ti\-fi\-ca\-tion permission handling
    \item \textbf{Token Management}: Automatic token generation and refresh handling
    \item \textbf{Message Listeners}: Foreground, background, and terminated state message handling
\end{itemize}

\subsubsection{Local Notifications Integration}
\begin{itemize}
    \item \textbf{Plugin Configuration}: Flutter local notifications plugin setup
    \item \textbf{Channel Management}: Android notification channel configuration
    \item \textbf{Custom Notifications}: Enhanced notification display with custom styling
    \item \textbf{Interaction Handling}: Notification tap handling and app navigation
\end{itemize}

\section{System Polish and Quality Assurance}

\subsection{Performance Optimization}
Sprint 5 included comprehensive performance optimization across all platform components:

\subsubsection{Backend Optimizations}
\begin{itemize}
    \item \textbf{Database Query Optimization}: Implemented efficient MongoDB queries with proper indexing
    \item \textbf{API Response Caching}: Added Redis caching for frequently accessed data
    \item \textbf{Connection Pooling}: Optimized database connection management
    \item \textbf{Memory Management}: Implemented proper memory cleanup and garbage collection
\end{itemize}

\subsubsection{Web Application Optimizations}
\begin{itemize}
    \item \textbf{Bundle Size Reduction}: Implemented lazy loading and code splitting
    \item \textbf{Asset Optimization}: Compressed images and optimized static assets
    \item \textbf{Caching Strategies}: Implemented browser caching and service worker caching
    \item \textbf{Responsive Design}: Optimized layouts for various screen sizes
\end{itemize}

\subsubsection{Mobile Application Optimizations}
\begin{itemize}
    \item \textbf{App Bundle Optimization}: Reduced APK/IPA size through asset optimization
    \item \textbf{Memory Usage}: Optimized widget rebuilds and state management
    \item \textbf{Network Efficiency}: Implemented request caching and offline capabilities
    \item \textbf{Battery Optimization}: Minimized background processing and location usage
\end{itemize}

\subsection{User Experience Polish}
Comprehensive UI/UX improvements were implemented across all platforms:

\subsubsection{Design Consistency}
\begin{itemize}
    \item \textbf{Design System}: Implemented consistent color schemes, typography, and spacing
    \item \textbf{Component Library}: Created reusable UI components across platforms
    \item \textbf{Animation Guidelines}: Consistent animations and transitions
    \item \textbf{Accessibility Standards}: WCAG 2.1 compliance for web and mobile accessibility
\end{itemize}

\subsubsection{User Interaction Improvements}
\begin{itemize}
    \item \textbf{Loading States}: Implemented skeleton screens and progress indicators
    \item \textbf{Error Handling}: User-friendly error messages and recovery options
    \item \textbf{Feedback Systems}: Toast notifications and confirmation dialogs
    \item \textbf{Navigation Enhancement}: Improved navigation flows and breadcrumbs
\end{itemize}

\subsection{Security Hardening}
Final security measures were implemented to ensure production readiness:

\subsubsection{Authentication Security}
\begin{itemize}
    \item \textbf{JWT Security}: Implemented secure token generation and validation
    \item \textbf{Session Management}: Secure session handling and timeout mechanisms
    \item \textbf{Password Security}: Enhanced password hashing and validation
    \item \textbf{Multi-Factor Authentication}: Secure 2FA implementation
\end{itemize}

\subsubsection{Data Protection}
\begin{itemize}
    \item \textbf{Encryption}: End-to-end encryption for sensitive medical data
    \item \textbf{HIPAA Compliance}: Healthcare data protection standards implementation
    \item \textbf{API Security}: Rate limiting, input validation, and SQL injection prevention
    \item \textbf{Firebase Security}: Secure Firebase configuration and access rules
\end{itemize}

\section{Testing and Quality Assurance}

\subsection{Comprehensive Testing Strategy}
Sprint 5 implemented extensive testing across all platform components:

\subsubsection{Backend Testing}
\begin{itemize}
    \item \textbf{Unit Testing}: Comprehensive unit tests for all service classes and use cases
    \item \textbf{Integration Testing}: API endpoint testing and database integration tests
    \item \textbf{Notification Testing}: FCM service testing with mock Firebase services
    \item \textbf{Performance Testing}: Load testing for concurrent notification delivery
\end{itemize}

\subsubsection{Frontend Testing}
\begin{itemize}
    \item \textbf{Component Testing}: Angular component testing with TestBed
    \item \textbf{Service Testing}: Firebase service and notification service testing
    \item \textbf{E2E Testing}: End-to-end testing with Cypress for critical user flows
    \item \textbf{Cross-Browser Testing}: Compatibility testing across major browsers
\end{itemize}

\subsubsection{Mobile Testing}
\begin{itemize}
    \item \textbf{Widget Testing}: Flutter widget testing for UI components
    \item \textbf{Integration Testing}: Firebase messaging integration testing
    \item \textbf{Device Testing}: Testing across various iOS and Android devices
    \item \textbf{Notification Testing}: Push notification testing in various app states
\end{itemize}

\section{Deployment and Production Readiness}

\subsection{Deployment Configuration}
Final deployment preparations included comprehensive environment setup:

\subsubsection{Backend Deployment}
\begin{itemize}
    \item \textbf{Environment Configuration}: Production environment variables and secrets management
    \item \textbf{Database Setup}: MongoDB production cluster configuration
    \item \textbf{Firebase Configuration}: Production Firebase project setup and service account management
    \item \textbf{Monitoring}: Application monitoring and logging configuration
\end{itemize}

\subsubsection{Web Application Deployment}
\begin{itemize}
    \item \textbf{Build Optimization}: Production build configuration with optimization flags
    \item \textbf{CDN Configuration}: Static asset delivery through content delivery network
    \item \textbf{SSL Configuration}: HTTPS configuration and security headers
    \item \textbf{Service Worker}: Production service worker for offline capabilities
\end{itemize}

\subsubsection{Mobile Application Deployment}
\begin{itemize}
    \item \textbf{App Store Preparation}: iOS App Store and Google Play Store submission preparation
    \item \textbf{Code Signing}: Production code signing certificates and provisioning profiles
    \item \textbf{Release Configuration}: Production build configuration and obfuscation
    \item \textbf{Push Notification Setup}: Production FCM configuration for mobile platforms
\end{itemize}

\section{Conclusion}
Sprint 5 successfully delivered a comprehensive notification management system integrated with Firebase Cloud Messaging, ensuring real-time communication across all platform users. The implementation provides robust notification delivery for prescription alerts, order status updates, and system announcements while maintaining high performance and security standards.

The final system polish phase ensured production readiness through comprehensive performance optimization, user experience improvements, security hardening, and extensive testing. The MED4SOLUTIONS platform now provides a complete, scalable, and secure solution for medication management and delivery with enterprise-grade notification capabilities.

The successful completion of Sprint 5 marks the achievement of all project objectives, delivering a fully functional digital health platform that addresses the identified challenges in medication management while providing innovative features such as AI-powered prescription processing and real-time delivery tracking.

